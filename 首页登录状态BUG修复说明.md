# 首页登录状态BUG修复说明

## 问题描述

用户退出登录后，返回首页时仍然会调用`getUser`方法请求用户信息，导致出现未登录报错。

## 问题原因分析

1. **store状态清理不完全**：在`LogOut`action中，虽然调用了`storage.clean()`，但没有清除store中的`userId`、`avatar`等状态
2. **登录状态检查不严格**：首页的`checkLoginAndInit`方法只检查了`userId`是否存在，没有同时检查`token`的有效性
3. **状态不一致**：退出登录后，`token`被清除但`userId`可能仍然存在，导致逻辑判断错误

## 修复方案

### 1. 修复store中的LogOut action (`store/modules/user.js`)

**修改前：**
```javascript
LogOut({ commit, state }) {
  return new Promise((resolve, reject) => {
    logout(state.token).then(() => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_PERMISSIONS', [])
      commit('SET_NAME','')
      removeToken()
      storage.clean()
      resolve()
    }).catch(error => {
      reject(error)
    })
  })
}
```

**修改后：**
```javascript
LogOut({ commit, state }) {
  return new Promise((resolve, reject) => {
    logout(state.token).then(() => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_PERMISSIONS', [])
      commit('SET_NAME','')
      commit('SET_AVATAR','')
      commit('SET_USER_ID','')  // 新增：清除userId
      removeToken()
      storage.clean()
      resolve()
    }).catch(error => {
      reject(error)
    })
  })
}
```

### 2. 修复首页登录状态检查逻辑 (`pages/index.vue`)

**修改前：**
```javascript
checkLoginAndInit() {
  this.userId = this.$store.state.user.userId ? this.userId = this.$store.state.user.userId : null
  if (this.userId) {
    this.isLogin = true;
    getUser(this.userId).then(res => {
      if (res.code === 200) {
        this.userInfo = res.data;
      } else {
        this.isLogin = false;
      }
    }).catch(() => {
      this.isLogin = false;
    });
  } else {
    this.isLogin = false;
  }
}
```

**修改后：**
```javascript
checkLoginAndInit() {
  // 检查token和userId是否都存在
  const token = this.$store.state.user.token
  const userId = this.$store.state.user.userId
  
  if (token && userId) {
    this.userId = userId
    this.isLogin = true;
    getUser(this.userId).then(res => {
      if (res.code === 200) {
        this.userInfo = res.data;
      } else {
        // 获取失败处理，清除登录状态
        this.clearLoginState();
      }
    }).catch(() => {
      // 请求失败，清除登录状态
      this.clearLoginState();
    });
  } else {
    // 没有token或userId，设置为未登录状态
    this.clearLoginState();
  }
},

// 清除登录状态
clearLoginState() {
  this.isLogin = false;
  this.userId = null;
  // 重置用户信息为默认值
  this.userInfo = {
    nickName: '用户未登录',
    money: 0.00,
    goldRaw: 0.00,
    goldBar: 0.00
  };
}
```

## 修复效果

1. **完全清除登录状态**：退出登录时，所有相关的用户状态都会被清除
2. **严格的登录检查**：同时检查token和userId，确保登录状态的一致性
3. **避免无效请求**：未登录状态下不会调用getUser方法
4. **统一的状态管理**：通过`clearLoginState`方法统一处理未登录状态

## 测试建议

1. **登录测试**：正常登录后，检查用户信息是否正确显示
2. **退出登录测试**：退出登录后，检查是否不再请求用户信息
3. **页面切换测试**：退出登录后切换到首页，确认不会出现报错
4. **重新登录测试**：退出后重新登录，确认功能正常

## 相关文件

- `store/modules/user.js` - 用户状态管理
- `pages/index.vue` - 首页组件
- `utils/auth.js` - 认证工具
- `utils/storage.js` - 存储工具

## 注意事项

此修复确保了登录状态的一致性，避免了退出登录后的无效API请求，提升了用户体验和应用的稳定性。
