<template>
	<view class="page-container">
		<u-swiper
			:list="swiperList"
			keyName="image"
			:autoplay="true"
			circular
			height="380rpx" 
			radius="0"
			img-mode="aspectFill" 
		></u-swiper>
		
		<view class="price-section">
			<view class="price-item">
				<text class="price-label">黄金销售</text>
				<text class="price-value sell-price">{{ goldPrice.sale }}</text>
			</view>
			<view class="price-item">
				<text class="price-label">黄金回购</text>
				<text class="price-value buy-back-price">{{ goldPrice.buyback }}</text>
			</view>
		</view>
		
		<view class="notice-section">
			<u-notice-bar :text="noticeText" icon="bell" speed="50"></u-notice-bar>
		</view>
		
		<view class="user-card card-panel">
			<view class="user-header">
				<text class="nickname">{{ userInfo.nickName }}</text>
				<button v-if="isLogin" class="recharge-btn">余额充值</button>
			</view>
			<view class="user-assets">
				<view class="asset-item">
					<text class="asset-value">{{ userInfo.money || '0.00' }} 元</text>
					<text class="asset-label">账户余额</text>
				</view>
				<view class="asset-item">
					<text class="asset-value">{{ userInfo.goldRaw || '0.00' }} g</text>
					<text class="asset-label">账户金料</text>
				</view>
				<view class="asset-item">
					<text class="asset-value">{{ userInfo.goldBar || '0.00' }} g</text>
					<text class="asset-label">账户金条</text>
				</view>
			</view>
		</view>
		
		<view class="action-buttons card-panel">
			<view class="button-item">
				<u-icon name="shopping-cart" size="32"></u-icon>
				<text class="button-text">购买</text>
			</view>
			<view class="button-item">
				<u-icon name="rmb-circle" size="32"></u-icon>
				<text class="button-text">回收</text>
			</view>
			<view class="button-item">
				<u-icon name="lock" size="32"></u-icon>
				<text class="button-text">存料</text>
			</view>
			<view class="button-item">
				<u-icon name="car" size="32"></u-icon>
				<text class="button-text">提货</text>
			</view>
		</view>

		<view class="seckill-card card-panel">
			<view class="seckill-header">
				<text class="seckill-title">限时秒杀</text>
			</view>
			<view class="seckill-content">
				<view class="seckill-image-placeholder"></view>
				<view class="seckill-details">
					<text class="item-name">鸿运金条</text>
					<text class="item-spec">规格: Au9999</text>
					<text class="item-spec">克重: 100.00g</text>
					<text class="item-spec">工费: 5元/克</text>
					<text class="item-price">限时: 78500.00元</text>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import { getUser } from "@/api/system/user.js"
	import { listSwiper } from "@/api/gold/swiper.js"
	import { listNotice } from "@/api/system/notice.js"
	import { baseUrl } from "@/config" // 注意：请确保您的 @/config 文件路径和导出是正确的

	export default {
		data() {
			return {
				userId: this.$store.state.user.userId,
				baseUrl,
				isLogin: false, // 登录状态
				swiperList: [], // 轮播图数据
				noticeText: '暂无公告', // 公告文本
				// 静态金价数据
				goldPrice: {
					sale: '774.50',
					buyback: '772.81'
				},
				// 用户信息
				userInfo: {
					nickName: '用户未登录',
					money: 0.00,
					goldRaw: 0.00,
					goldBar: 0.00
				}
			}
		},
		onLoad() {
			this.checkLoginAndInit();
			this.getSwiperData();
			this.getNoticeData();
		},
		methods: {
			// 检查登录状态并初始化用户信息
			checkLoginAndInit() {
				if (this.userId) {
					this.isLogin = true;
					getUser(this.userId).then(res => {
						if (res.code === 200) {
							this.userInfo = res.data;
						} else {
							// 获取失败处理
							this.isLogin = false; // token可能失效，重置为未登录
						}
					}).catch(() => {
						this.isLogin = false;
					});
				} else {
					this.isLogin = false;
					// 未登录时，data中已设置默认值
				}
			},
			
			// 获取轮播图数据
			getSwiperData() {
				listSwiper().then(res => {
					if (res.code === 200 && res.rows) {
						this.swiperList = res.rows.map(item => {
							return {
								...item,
								// u-swiper 的 keyName 默认为 url, 我们需要拼接图片地址
								// 如果您在组件上指定了 keyName="image", 这里也应该用 image
								image: this.baseUrl + item.pic
							};
						});
					}
				});
			},

			// 获取公告数据
			getNoticeData() {
				listNotice({ noticeType: "1" }).then(res => {
					if (res.code === 200 && res.rows && res.rows.length > 0) {
						// 只显示第一条公告的标题
						this.noticeText = res.rows[0].noticeTitle;
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	// 页面根节点
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
		padding-bottom: 20rpx;

		// 卡片面板统一样式
		.card-panel {
			background-color: #ffffff;
			margin: 20rpx;
			padding: 30rpx;
			border-radius: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
		}
		
		// 价格区域
		.price-section {
			display: flex;
			justify-content: space-around;
			align-items: center;
			background-color: #ffffff;
			padding: 20rpx;

			.price-item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.price-label {
					font-size: 28rpx;
					color: #606266;
				}

				.price-value {
					font-size: 40rpx;
					font-weight: bold;
					margin-top: 10rpx;

					&.sell-price {
						color: #fa3534;
					}

					&.buy-back-price {
						color: #19be6b;
					}
				}
			}
		}
		
		// 公告区域
		.notice-section {
			margin: 0 20rpx;
		}

		// 用户信息卡片
		.user-card {
			background-color: #eaf5ff;

			.user-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 40rpx;

				.nickname {
					font-size: 36rpx;
					font-weight: bold;
					color: #303133;
				}

				.recharge-btn {
					background-color: #3c9cff;
					color: #ffffff;
					font-size: 24rpx;
					padding: 0 24rpx;
					margin: 0;
					line-height: 52rpx;
					height: 52rpx;
					border-radius: 50rpx;
					
					&::after {
						border: none;
					}
				}
			}

			.user-assets {
				display: flex;
				justify-content: space-between;
				text-align: center;

				.asset-item {
					display: flex;
					flex-direction: column;
					flex: 1;

					.asset-value {
						font-size: 32rpx;
						font-weight: 500;
						color: #303133;
					}

					.asset-label {
						font-size: 24rpx;
						color: #909399;
						margin-top: 10rpx;
					}
				}
			}
		}
		
		// 操作按钮区域
		.action-buttons {
			display: flex;
			justify-content: space-around;
			align-items: center;

			.button-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				color: #303133;

				.button-text {
					margin-top: 10rpx;
					font-size: 26rpx;
				}
			}
		}
		
		// 限时秒杀卡片
		.seckill-card {
			background-color: #eaf5ff;

			.seckill-header {
				margin-bottom: 20rpx;

				.seckill-title {
					font-size: 32rpx;
					font-weight: bold;
				}
			}

			.seckill-content {
				display: flex;
				align-items: center;

				.seckill-image-placeholder {
					width: 200rpx;
					height: 200rpx;
					background-color: #c8dcf0;
					border-radius: 8rpx;
					margin-right: 20rpx;
				}

				.seckill-details {
					display: flex;
					flex-direction: column;
					font-size: 24rpx;
					color: #606266;
					line-height: 1.6;

					.item-name {
						font-size: 30rpx;
						font-weight: bold;
						color: #303133;
						margin-bottom: 5rpx;
					}

					.item-price {
						color: #fa3534;
						font-weight: bold;
						font-size: 28rpx;
						margin-top: 5rpx;
					}
				}
			}
		}
	}
</style>